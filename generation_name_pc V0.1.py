import random
import string

def generate_pc_name(letter='L'):
    prefix = f"PUMA-{letter}-"
    suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
    return prefix + suffix

# Example usage
choice = input("Enter the letter for your PC name (L or D): ").strip().upper()
if choice not in ['L', 'D']:
    print("Invalid choice. Using 'L' by default.")
    choice = 'L'
print(generate_pc_name(choice))