@echo off
echo ========================================
echo   PC Name Generator - Build Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo ✓ Python found
echo.

REM Install PyInstaller if not already installed
echo Installing PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ERROR: Failed to install PyInstaller
    pause
    exit /b 1
)

echo ✓ PyInstaller installed
echo.

REM Build the executable
echo Building standalone executable...
pyinstaller --onefile --console --name "PC_Name_Generator" generation_name_pc.py
if errorlevel 1 (
    echo ERROR: Failed to build executable
    pause
    exit /b 1
)

echo.
echo ========================================
echo   BUILD SUCCESSFUL!
echo ========================================
echo.
echo The executable has been created in the 'dist' folder:
echo   dist\PC_Name_Generator.exe
echo.
echo You can now copy this .exe file to any Windows machine
echo and run it without needing Python installed.
echo.
pause
