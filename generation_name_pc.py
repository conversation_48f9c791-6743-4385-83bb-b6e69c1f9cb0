#!/usr/bin/env python3
"""
PC Name Generator - Professional Edition
Generates unique PC names with PUMA prefix based on system serial number or random generation.
"""

import string
import subprocess
import secrets
import os
import sys
from datetime import datetime

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """Display a professional banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    🖥️  PC NAME GENERATOR 🖥️                 ║
║                      Professional Edition                    ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
{Colors.BLUE}┌─ System Information ─────────────────────────────────────────┐{Colors.END}
{Colors.YELLOW}│ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S'):<44} │{Colors.END}
{Colors.YELLOW}│ Platform:  Windows                                           │{Colors.END}
{Colors.BLUE}└──────────────────────────────────────────────────────────────┘{Colors.END}
"""
    print(banner)

def print_loading_animation():
    """Display a loading animation."""
    import time
    print(f"{Colors.YELLOW}🔍 Scanning system hardware...{Colors.END}")
    for i in range(3):
        print(f"{Colors.CYAN}{'.' * (i + 1)}{Colors.END}", end='\r')
        time.sleep(0.5)
    print(" " * 30, end='\r')  # Clear the line

def get_serial_number():
    """
    Retrieve the system's BIOS serial number.
    Returns None if serial number is unavailable or is a placeholder.
    """
    try:
        print_loading_animation()
        result = subprocess.check_output('wmic bios get serialnumber', shell=True, stderr=subprocess.DEVNULL)
        lines = [line.strip() for line in result.decode().splitlines() if line.strip()]

        serial = None
        for line in lines:
            if line.lower() != "serialnumber":
                serial = line
                break

        # Check if serial is a placeholder value
        placeholder_values = [
            "to be filled by o.e.m.",
            "default string",
            "system serial number",
            "not specified",
            "none"
        ]

        if serial and serial.lower() in placeholder_values:
            return None

        return serial if serial else None

    except Exception:
        return None

def generate_pc_name(letter, serial):
    """
    Generate a PC name with format: PUMA-{letter}-{suffix}
    Uses serial number if available, otherwise generates random suffix.
    """
    alphabet = string.ascii_uppercase + string.digits

    if serial and len(serial) >= 6:
        # Remove spaces and special characters, then take first 6 alphanumeric characters
        clean_serial = ''.join(c for c in serial.upper() if c.isalnum())
        if len(clean_serial) >= 6:
            suffix = clean_serial[:6]
        else:
            # Pad with random characters if serial is too short
            suffix = clean_serial + ''.join(secrets.choice(alphabet) for _ in range(6 - len(clean_serial)))
    else:
        # Generate completely random suffix
        suffix = ''.join(secrets.choice(alphabet) for _ in range(6))

    return f"PUMA-{letter}-{suffix}"

def get_user_choice():
    """Get and validate user input for PC name letter."""
    while True:
        print(f"\n{Colors.BLUE}┌─ Configuration ──────────────────────────────────────────────┐{Colors.END}")
        print(f"{Colors.BLUE}│{Colors.END} Select PC name type:")
        print(f"{Colors.BLUE}│{Colors.END}   {Colors.GREEN}[L]{Colors.END} - Laptop/Mobile device")
        print(f"{Colors.BLUE}│{Colors.END}   {Colors.GREEN}[D]{Colors.END} - Desktop/Workstation")
        print(f"{Colors.BLUE}└──────────────────────────────────────────────────────────────┘{Colors.END}")

        choice = input(f"\n{Colors.YELLOW}Enter your choice (L/D): {Colors.END}").strip().upper()

        if choice in ['L', 'D']:
            return choice
        elif choice == '':
            print(f"{Colors.YELLOW}⚠️  No input provided. Using 'L' (Laptop) by default.{Colors.END}")
            return 'L'
        else:
            print(f"{Colors.RED}❌ Invalid choice '{choice}'. Please enter 'L' or 'D'.{Colors.END}")

def display_results(pc_name, serial, choice):
    """Display the generated results in a formatted way."""
    device_type = "Laptop/Mobile" if choice == 'L' else "Desktop/Workstation"
    serial_status = "Hardware-based" if serial else "Randomly generated"

    print(f"\n{Colors.GREEN}{Colors.BOLD}✅ PC Name Generated Successfully!{Colors.END}")
    print(f"\n{Colors.BLUE}┌─ Results ────────────────────────────────────────────────────┐{Colors.END}")
    print(f"{Colors.BLUE}│{Colors.END} {Colors.BOLD}Generated PC Name:{Colors.END} {Colors.CYAN}{Colors.BOLD}{pc_name}{Colors.END}")
    print(f"{Colors.BLUE}│{Colors.END} Device Type:       {device_type}")
    print(f"{Colors.BLUE}│{Colors.END} Suffix Type:       {serial_status}")
    print(f"{Colors.BLUE}│{Colors.END} Serial Number:     {serial if serial else 'Not detected'}")
    print(f"{Colors.BLUE}└──────────────────────────────────────────────────────────────┘{Colors.END}")

def main():
    """Main program execution."""
    try:
        clear_screen()
        print_banner()

        # Get user choice
        choice = get_user_choice()

        # Get serial number
        print(f"\n{Colors.CYAN}🔧 Retrieving system information...{Colors.END}")
        serial = get_serial_number()

        # Generate PC name
        pc_name = generate_pc_name(choice, serial)

        # Display results
        display_results(pc_name, serial, choice)

        # Footer
        print(f"\n{Colors.BLUE}┌─ Additional Information ─────────────────────────────────────┐{Colors.END}")
        print(f"{Colors.BLUE}│{Colors.END} • This name can be used for Windows computer naming")
        print(f"{Colors.BLUE}│{Colors.END} • Maximum length: 15 characters (Windows NetBIOS limit)")
        print(f"{Colors.BLUE}│{Colors.END} • Format: PUMA-[L/D]-[6-character suffix]")
        print(f"{Colors.BLUE}└──────────────────────────────────────────────────────────────┘{Colors.END}")

        print(f"\n{Colors.GREEN}Thank you for using PC Name Generator! 🚀{Colors.END}")

    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}⚠️  Operation cancelled by user.{Colors.END}")
        sys.exit(0)
    except Exception as e:
        print(f"\n{Colors.RED}❌ An unexpected error occurred: {str(e)}{Colors.END}")
        sys.exit(1)

if __name__ == "__main__":
    main()