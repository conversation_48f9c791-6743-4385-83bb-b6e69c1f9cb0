import random
import string
import subprocess
print("####### generator a pc name #######")
def get_serial_number():
    try:
        result = subprocess.check_output('wmic bios get serialnumber', shell=True)
        lines = [line.strip() for line in result.decode().splitlines() if line.strip()]
        serial = None
        for line in lines:
            if line.lower() != "serialnumber":
                serial = line
                break
        return serial if serial else None
    except Exception as e:
        return None

def generate_pc_name(letter, serial):
    import secrets
    alphabet = string.ascii_uppercase + string.digits
    if serial and len(serial) >= 6:
        suffix = serial[:6].upper()
    else:
        suffix = ''.join(secrets.choice(alphabet) for _ in range(6))
    prefix = f"PUMA-{letter}-"
    return prefix + suffix

choice = input("Enter the letter for your PC name (L or D): ").strip().upper()
if choice not in ['L', 'D']:
    print("Invalid choice. Using 'L' by default.")
    choice = 'L'

serial = get_serial_number()
print("Generated PC Name:", generate_pc_name(choice, serial))
print("Serial Number:", serial if serial else "Serial Number could not be detected.")